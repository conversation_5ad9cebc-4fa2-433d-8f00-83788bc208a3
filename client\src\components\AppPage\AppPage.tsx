import React, { useState } from 'react';
import {
  <PERSON>,
  App<PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  IconButton,
  Typography,
  useTheme,
  useMediaQuery,
  CssBaseline,
} from '@mui/material';
import {
  Menu as MenuIcon,
  Notifications,
  Search,
} from '@mui/icons-material';
import { styled } from '@mui/material/styles';
import NavigationDrawer from '../NavigationDrawer/NavigationDrawer';
import { User } from '../../types';

const DRAWER_WIDTH = 280;
const MINI_DRAWER_WIDTH = 64;

interface AppPageProps {
  children: React.ReactNode;
  user: User | null;
  onLogout: () => void;
  title?: string;
  showAppBar?: boolean;
}

// Styled Components
const Main = styled('main', {
  shouldForwardProp: (prop) => !['open', 'mini'].includes(prop as string)
})<{ open?: boolean; mini?: boolean }>(({ theme, open, mini }) => ({
  flexGrow: 1,
  transition: theme.transitions.create('margin', {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  marginLeft: 0,
  [theme.breakpoints.up('md')]: {
    marginLeft: open ? (mini ? MINI_DRAWER_WIDTH : DRAWER_WIDTH) : 0,
    transition: theme.transitions.create('margin', {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
}));

const StyledAppBar = styled(AppBar, {
  shouldForwardProp: (prop) => prop !== 'open',
})<{ open?: boolean }>(({ theme, open }) => ({
  transition: theme.transitions.create(['margin', 'width'], {
    easing: theme.transitions.easing.sharp,
    duration: theme.transitions.duration.leavingScreen,
  }),
  [theme.breakpoints.up('md')]: {
    width: open ? `calc(100% - ${DRAWER_WIDTH}px)` : '100%',
    marginLeft: open ? DRAWER_WIDTH : 0,
    transition: theme.transitions.create(['margin', 'width'], {
      easing: theme.transitions.easing.easeOut,
      duration: theme.transitions.duration.enteringScreen,
    }),
  },
}));

const ContentContainer = styled(Box)(({ theme }) => ({
  padding: theme.spacing(3),
  minHeight: 'calc(100vh - 64px)', // Account for app bar height
  backgroundColor: theme.palette.background.default,
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(2),
  },
}));

const AppPage: React.FC<AppPageProps> = ({
  children,
  user,
  onLogout,
  title,
  showAppBar = true,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [drawerOpen, setDrawerOpen] = useState(!isMobile);
  const [miniDrawer, setMiniDrawer] = useState(false);

  const handleDrawerToggle = () => {
    setDrawerOpen(!drawerOpen);
  };

  const handleDrawerClose = () => {
    if (isMobile) {
      setDrawerOpen(false);
    }
  };

  const handleDrawerOpen = () => {
    setDrawerOpen(true);
  };

  const handleToggleMini = () => {
    setMiniDrawer(!miniDrawer);
  };

  return (
    <Box sx={{ display: 'flex' }}>
      <CssBaseline />
      
      {/* App Bar */}
      {showAppBar && (
        <StyledAppBar 
          position="fixed" 
          open={drawerOpen && !isMobile}
          elevation={1}
          sx={{
            backgroundColor: theme.palette.background.paper,
            color: theme.palette.text.primary,
            borderBottom: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Toolbar>
            <IconButton
              color="inherit"
              aria-label="toggle drawer"
              onClick={handleDrawerToggle}
              edge="start"
              sx={{ 
                mr: 2,
                ...(drawerOpen && !isMobile && { display: 'none' }),
              }}
            >
              <MenuIcon />
            </IconButton>
            
            <Typography 
              variant="h6" 
              noWrap 
              component="div" 
              sx={{ flexGrow: 1, fontWeight: 600 }}
            >
              {title || 'Dashboard'}
            </Typography>

            {/* App Bar Actions */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton color="inherit" size="large">
                <Search />
              </IconButton>
              <IconButton color="inherit" size="large">
                <Notifications />
              </IconButton>
            </Box>
          </Toolbar>
        </StyledAppBar>
      )}

      {/* Navigation Drawer */}
      <NavigationDrawer
        open={drawerOpen}
        onClose={handleDrawerClose}
        onOpen={handleDrawerOpen}
        user={user}
        onLogout={onLogout}
        variant={isMobile ? 'temporary' : 'persistent'}
        mini={!isMobile && miniDrawer}
        onToggleMini={handleToggleMini}
      />

      {/* Main Content */}
      <Main open={drawerOpen && !isMobile} mini={miniDrawer}>
        {showAppBar && <Toolbar />} {/* Spacer for fixed app bar */}
        <ContentContainer>
          {children}
        </ContentContainer>
      </Main>
    </Box>
  );
};

export default AppPage;
