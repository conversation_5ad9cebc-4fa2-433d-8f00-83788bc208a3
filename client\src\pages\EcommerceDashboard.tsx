import React from 'react';
import { Typography, Box, Card, CardContent } from '@mui/material';
import AppPage from '../components/AppPage';
import { DashboardProps } from '../types';

const EcommerceDashboard: React.FC<DashboardProps> = ({ user, onLogout }) => {

  return (
    <AppPage user={user} onLogout={onLogout} title="eCommerce Dashboard">
      <Box>
        <Typography variant="h4" gutterBottom>
          eCommerce Dashboard
        </Typography>
        <Card>
          <CardContent>
            <Typography variant="body1">
              This is the eCommerce dashboard page. Sales data and metrics will be displayed here.
            </Typography>
          </CardContent>
        </Card>
      </Box>
    </AppPage>
  );
};

export default EcommerceDashboard;
