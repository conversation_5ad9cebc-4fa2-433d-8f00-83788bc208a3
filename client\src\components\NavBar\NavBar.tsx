import React, { useState, useEffect } from 'react';
import { styled } from '@mui/material/styles';
import { Box, Container, Typography, IconButton, Drawer, List, ListItem, ListItemButton, ListItemText } from '@mui/material';
import { Close as CloseIcon } from '@mui/icons-material';
import { useTranslation } from 'react-i18next';
import { User } from '../../types';
import LogoSection from './LogoSection';
import NavLinks from './NavLinks';
import ProfileSection from './ProfileSection';

// Styled Components
interface NavBarContainerProps {
  scrolled?: boolean;
}

const NavBarContainer = styled(Box, {
  shouldForwardProp: (prop) => prop !== 'scrolled',
})<NavBarContainerProps>(({ theme, scrolled }) => ({
  position: 'fixed',
  top: 0,
  left: 0,
  right: 0,
  zIndex: 1100,
  background: scrolled
    ? `linear-gradient(135deg,
        ${theme.palette.primary.main}F5 0%,
        ${theme.palette.secondary.main}F5 50%,
        #9C27B0F5 100%
      )`
    : `linear-gradient(135deg,
        ${theme.palette.primary.main}E6 0%,
        ${theme.palette.secondary.main}E6 50%,
        #9C27B0E6 100%
      )`,
  backdropFilter: 'blur(10px)',
  borderRadius: '0 0 24px 24px',
  margin: '0 16px',
  transition: theme.transitions.create(['background', 'box-shadow'], {
    duration: theme.transitions.duration.short,
  }),
  boxShadow: scrolled ? '0 4px 20px rgba(0, 0, 0, 0.1)' : 'none',
  [theme.breakpoints.down('sm')]: {
    margin: '0 8px',
    borderRadius: '0 0 16px 16px',
  },
}));

const NavBarContent = styled(Container)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  padding: theme.spacing(1.5, 2),
  [theme.breakpoints.down('sm')]: {
    padding: theme.spacing(1, 1.5),
  },
}));

const MobileDrawer = styled(Drawer)(({ theme }) => ({
  '& .MuiDrawer-paper': {
    width: 280,
    background: `linear-gradient(135deg,
      ${theme.palette.primary.main}F0 0%,
      ${theme.palette.secondary.main}F0 50%,
      #9C27B0F0 100%
    )`,
    backdropFilter: 'blur(10px)',
    color: 'white',
  },
}));

const MobileNavList = styled(List)(({ theme }) => ({
  padding: theme.spacing(2, 0),
}));

const MobileNavItem = styled(ListItemButton)(({ theme }) => ({
  margin: theme.spacing(0.5, 2),
  borderRadius: theme.spacing(1),
  '&:hover': {
    backgroundColor: 'rgba(255, 255, 255, 0.15)',
  },
}));

// Navigation items configuration
const navigationItems = [
  { key: 'home', labelKey: 'nav.home' },
  { key: 'about', labelKey: 'nav.about' },
  { key: 'pricing', labelKey: 'nav.pricing' },
];

interface NavBarProps {
  user?: User | null;
  onNavigate?: (section: string) => void;
  onProfileClick?: () => void;
  onLoginClick?: () => void;
  onRegisterClick?: () => void;
}

const NavBar: React.FC<NavBarProps> = ({ user, onNavigate, onProfileClick, onLoginClick, onRegisterClick }) => {
  const { t } = useTranslation();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      setScrolled(isScrolled);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleNavClick = (section: string) => {
    setMobileMenuOpen(false); // Close mobile menu when navigating
    if (onNavigate) {
      onNavigate(section);
    } else {
      // Default behavior: scroll to section
      const element = document.getElementById(`${section}-section`);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });
      }
    }
  };

  const handleLogoClick = () => {
    setMobileMenuOpen(false);
    window.scrollTo({ top: 0, behavior: 'smooth' });
  };

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen);
  };

  return (
    <NavBarContainer scrolled={scrolled}>
      <NavBarContent maxWidth="lg">
        <LogoSection onClick={handleLogoClick} />

        <NavLinks
          navigationItems={navigationItems}
          onNavClick={handleNavClick}
        />

        <ProfileSection
          user={user}
          onProfileClick={onProfileClick}
          onMobileMenuClick={toggleMobileMenu}
          onLoginClick={onLoginClick}
          onRegisterClick={onRegisterClick}
        />
      </NavBarContent>

      {/* Mobile Navigation Drawer */}
      <MobileDrawer
        anchor="right"
        open={mobileMenuOpen}
        onClose={() => setMobileMenuOpen(false)}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', p: 2 }}>
          <Typography variant="h6" sx={{ color: 'white', fontWeight: 600 }}>
            {t('app.title')}
          </Typography>
          <IconButton onClick={() => setMobileMenuOpen(false)} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </Box>
        <MobileNavList>
          {navigationItems.map((item) => (
            <ListItem key={item.key} disablePadding>
              <MobileNavItem onClick={() => handleNavClick(item.key)}>
                <ListItemText
                  primary={t(item.labelKey)}
                  sx={{
                    '& .MuiListItemText-primary': {
                      fontWeight: 500,
                      fontSize: '1rem'
                    }
                  }}
                />
              </MobileNavItem>
            </ListItem>
          ))}
        </MobileNavList>
      </MobileDrawer>
    </NavBarContainer>
  );
};

export default NavBar;
