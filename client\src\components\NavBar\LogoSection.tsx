import React from 'react';
import { styled } from '@mui/material/styles';
import { Box, Typography } from '@mui/material';
import { useTranslation } from 'react-i18next';

// Styled Components
const LogoSectionContainer = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  gap: theme.spacing(1),
  cursor: 'pointer',
  transition: theme.transitions.create(['transform'], {
    duration: theme.transitions.duration.short,
  })
}));

const LogoContainer = styled(Box)(() => ({
  width: 32,
  height: 32,
  borderRadius: '50%',
  backgroundColor: 'white',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  padding: 4,
  '&:hover': {
      transform: 'scale(1.05) rotate(5deg)',
    },
}));

const LogoImage = styled('img')(() => ({
  width: '100%',
  height: '100%',
  objectFit: 'contain',
}));

const BrandText = styled(Typography)(({ theme }) => ({
  color: 'white',
  fontWeight: 600,
  fontSize: '1.1rem',
  [theme.breakpoints.down('sm')]: {
    fontSize: '1rem',
  },
}));

// Component Props Interface
interface LogoSectionProps {
  onClick?: () => void;
  logoSrc?: string;
  altText?: string;
}

const LogoSection: React.FC<LogoSectionProps> = ({ 
  onClick, 
  logoSrc = "/Pigeon Squad Logo.png", 
  altText = "Logo" 
}) => {
  const { t } = useTranslation();

  return (
    <LogoSectionContainer onClick={onClick}>
      <LogoContainer>
        <LogoImage src={logoSrc} alt={altText} />
      </LogoContainer>
      <BrandText variant="h6">
        {t('app.title')}
      </BrandText>
    </LogoSectionContainer>
  );
};

export default LogoSection;
